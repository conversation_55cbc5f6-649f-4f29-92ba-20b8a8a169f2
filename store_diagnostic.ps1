# Microsoft Store Diagnostic Script
Write-Host "=== Microsoft Store Diagnostic Report ===" -ForegroundColor Cyan

# Check Windows version
Write-Host "`n1. Windows Version:" -ForegroundColor Yellow
$osInfo = Get-ComputerInfo | Select-Object WindowsProductName, WindowsVersion, WindowsBuildLabEx
Write-Host "   Product: $($osInfo.WindowsProductName)" -ForegroundColor White
Write-Host "   Version: $($osInfo.WindowsVersion)" -ForegroundColor White
Write-Host "   Build: $($osInfo.WindowsBuildLabEx)" -ForegroundColor White

# Check all store-related packages
Write-Host "`n2. Store-related packages (Current User):" -ForegroundColor Yellow
$storePackages = Get-AppxPackage | Where-Object {$_.Name -like "*Store*"}
if ($storePackages) {
    $storePackages | ForEach-Object {
        Write-Host "   - $($_.Name) v$($_.Version) [$($_.Status)]" -ForegroundColor Green
    }
} else {
    Write-Host "   - No store packages found for current user" -ForegroundColor Red
}

# Check Windows capabilities
Write-Host "`n3. Windows Store Capability:" -ForegroundColor Yellow
try {
    $capability = Get-WindowsCapability -Online | Where-Object {$_.Name -like "*Store*"}
    if ($capability) {
        $capability | ForEach-Object {
            Write-Host "   - $($_.Name): $($_.State)" -ForegroundColor White
        }
    } else {
        Write-Host "   - No Store capability found" -ForegroundColor Red
    }
} catch {
    Write-Host "   - Error checking capabilities: $($_.Exception.Message)" -ForegroundColor Red
}

# Check Windows features
Write-Host "`n4. Windows Features:" -ForegroundColor Yellow
try {
    $features = Get-WindowsOptionalFeature -Online | Where-Object {$_.FeatureName -like "*Store*"}
    if ($features) {
        $features | ForEach-Object {
            Write-Host "   - $($_.FeatureName): $($_.State)" -ForegroundColor White
        }
    } else {
        Write-Host "   - No Store features found" -ForegroundColor Red
    }
} catch {
    Write-Host "   - Error checking features: $($_.Exception.Message)" -ForegroundColor Red
}

# Test store launch
Write-Host "`n5. Store Launch Test:" -ForegroundColor Yellow
try {
    Start-Process "ms-windows-store:" -ErrorAction Stop
    Write-Host "   - Store protocol launched successfully" -ForegroundColor Green
} catch {
    Write-Host "   - Store protocol failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Check if store executable exists
Write-Host "`n6. Store Executable Check:" -ForegroundColor Yellow
$storePaths = @(
    "$env:LOCALAPPDATA\Microsoft\WindowsApps\Microsoft.WindowsStore_*",
    "$env:ProgramFiles\WindowsApps\Microsoft.WindowsStore_*"
)

$found = $false
foreach ($path in $storePaths) {
    $matches = Get-ChildItem $path -ErrorAction SilentlyContinue
    if ($matches) {
        $matches | ForEach-Object {
            Write-Host "   - Found: $($_.FullName)" -ForegroundColor Green
            $found = $true
        }
    }
}

if (-not $found) {
    Write-Host "   - No store executable found" -ForegroundColor Red
}

Write-Host "`n=== MANUAL STEPS TO TRY ===" -ForegroundColor Cyan
Write-Host "If the Microsoft Store is still not working, try these steps manually:" -ForegroundColor Yellow
Write-Host ""
Write-Host "METHOD 1 - Windows Settings:" -ForegroundColor Green
Write-Host "1. Press Windows + I to open Settings" -ForegroundColor White
Write-Host "2. Go to Apps > Optional Features" -ForegroundColor White
Write-Host "3. Click 'View Features'" -ForegroundColor White
Write-Host "4. Search for 'Microsoft Store' and install it" -ForegroundColor White
Write-Host ""
Write-Host "METHOD 2 - PowerShell (Run as Administrator):" -ForegroundColor Green
Write-Host "1. Right-click Start button > Windows PowerShell (Admin)" -ForegroundColor White
Write-Host "2. Run: Get-AppxPackage -AllUsers Microsoft.WindowsStore* | Foreach {Add-AppxPackage -DisableDevelopmentMode -Register `"`$(`$_.InstallLocation)\AppXManifest.xml`"}" -ForegroundColor White
Write-Host ""
Write-Host "METHOD 3 - DISM (Run as Administrator):" -ForegroundColor Green
Write-Host "1. Right-click Start button > Windows PowerShell (Admin)" -ForegroundColor White
Write-Host "2. Run: dism /online /add-capability /capabilityname:Microsoft.Windows.Store~~~~*******" -ForegroundColor White
Write-Host ""
Write-Host "METHOD 4 - Windows Update:" -ForegroundColor Green
Write-Host "1. Press Windows + I > Update & Security > Windows Update" -ForegroundColor White
Write-Host "2. Click 'Check for updates' and install all available updates" -ForegroundColor White
Write-Host "3. Restart and try again" -ForegroundColor White
Write-Host ""
Write-Host "METHOD 5 - Reset Windows (Last Resort):" -ForegroundColor Green
Write-Host "1. Press Windows + I > Update & Security > Recovery" -ForegroundColor White
Write-Host "2. Under 'Reset this PC', click 'Get started'" -ForegroundColor White
Write-Host "3. Choose 'Keep my files' and follow the prompts" -ForegroundColor White

Write-Host "`n=== Diagnostic Complete ===" -ForegroundColor Cyan
Read-Host "Press Enter to close"

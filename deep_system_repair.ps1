# DEEP METHOD 3: System File Integrity and Component Store Repair
# This script performs comprehensive system-level repairs for Microsoft Store
# MUST BE RUN AS ADMINISTRATOR

param(
    [switch]$FullScan,
    [switch]$Verbose
)

$ErrorActionPreference = "Continue"
$VerbosePreference = if ($Verbose) { "Continue" } else { "SilentlyContinue" }

Write-Host "=== DEEP SYSTEM FILE INTEGRITY AND COMPONENT STORE REPAIR ===" -ForegroundColor Cyan
Write-Host "This script performs comprehensive system-level repairs" -ForegroundColor Yellow
Write-Host "Estimated time: 30-60 minutes (depending on system)" -ForegroundColor Yellow

# Check administrator privileges
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "CRITICAL ERROR: Administrator privileges required!" -ForegroundColor Red
    exit 1
}

function Write-LogMessage {
    param([string]$Message, [string]$Color = "White")
    $timestamp = Get-Date -Format "HH:mm:ss"
    Write-Host "[$timestamp] $Message" -ForegroundColor $Color
}

function Invoke-CommandWithProgress {
    param([string]$Command, [string]$Description, [int]$TimeoutMinutes = 30)
    
    Write-LogMessage "Starting: $Description" "Yellow"
    $startTime = Get-Date
    
    try {
        $process = Start-Process -FilePath "cmd.exe" -ArgumentList "/c $Command" -NoNewWindow -PassThru -RedirectStandardOutput "temp_output.txt" -RedirectStandardError "temp_error.txt"
        
        # Monitor progress
        do {
            Start-Sleep -Seconds 10
            $elapsed = (Get-Date) - $startTime
            Write-LogMessage "  Progress: $([math]::Round($elapsed.TotalMinutes, 1)) minutes elapsed..." "Cyan"
        } while (-not $process.HasExited -and $elapsed.TotalMinutes -lt $TimeoutMinutes)
        
        if (-not $process.HasExited) {
            $process.Kill()
            Write-LogMessage "Command timed out after $TimeoutMinutes minutes: $Description" "Red"
            return $false
        }
        
        $exitCode = $process.ExitCode
        if ($exitCode -eq 0) {
            Write-LogMessage "Completed successfully: $Description" "Green"
            return $true
        } else {
            Write-LogMessage "Completed with errors (Exit: $exitCode): $Description" "Yellow"
            return $false
        }
    } catch {
        Write-LogMessage "Failed: $Description - $($_.Exception.Message)" "Red"
        return $false
    } finally {
        # Clean up temp files
        Remove-Item "temp_output.txt" -ErrorAction SilentlyContinue
        Remove-Item "temp_error.txt" -ErrorAction SilentlyContinue
    }
}

Write-LogMessage "=== PHASE 1: SYSTEM FILE CHECKER (SFC) ===" "Cyan"

# Run SFC scan
Write-LogMessage "Running System File Checker scan..." "Yellow"
$sfcSuccess = Invoke-CommandWithProgress "sfc /scannow" "System File Checker scan" 20

if ($sfcSuccess) {
    Write-LogMessage "SFC scan completed successfully" "Green"
} else {
    Write-LogMessage "SFC scan encountered issues - continuing with repair" "Yellow"
}

# Check SFC log for Store-related issues
Write-LogMessage "Analyzing SFC log for Store-related issues..." "Yellow"
try {
    $sfcLog = Get-Content "$env:WINDIR\Logs\CBS\CBS.log" -ErrorAction SilentlyContinue | Select-String -Pattern "Store|AppX" -SimpleMatch
    if ($sfcLog) {
        Write-LogMessage "Found $($sfcLog.Count) Store-related entries in SFC log" "Cyan"
        $sfcLog | Select-Object -First 5 | ForEach-Object {
            Write-LogMessage "  SFC: $($_.Line)" "White"
        }
    }
} catch {
    Write-LogMessage "Could not analyze SFC log: $($_.Exception.Message)" "Yellow"
}

Write-LogMessage "=== PHASE 2: DISM COMPONENT STORE ANALYSIS ===" "Cyan"

# Check component store health
Write-LogMessage "Checking component store health..." "Yellow"
$dismCheckSuccess = Invoke-CommandWithProgress "dism /online /cleanup-image /checkhealth" "DISM component store health check" 10

# Scan component store health
Write-LogMessage "Scanning component store health (detailed)..." "Yellow"
$dismScanSuccess = Invoke-CommandWithProgress "dism /online /cleanup-image /scanhealth" "DISM component store health scan" 15

Write-LogMessage "=== PHASE 3: DISM COMPONENT STORE REPAIR ===" "Cyan"

# Restore component store health
Write-LogMessage "Restoring component store health..." "Yellow"
$dismRestoreSuccess = Invoke-CommandWithProgress "dism /online /cleanup-image /restorehealth" "DISM component store restore" 30

if ($dismRestoreSuccess) {
    Write-LogMessage "DISM component store restore completed successfully" "Green"
} else {
    Write-LogMessage "DISM restore encountered issues - trying alternative sources" "Yellow"
    
    # Try with Windows Update as source
    Write-LogMessage "Attempting DISM restore with Windows Update source..." "Yellow"
    Invoke-CommandWithProgress "dism /online /cleanup-image /restorehealth /source:WU" "DISM restore with Windows Update" 30
}

Write-LogMessage "=== PHASE 4: COMPONENT STORE CLEANUP ===" "Cyan"

# Clean up component store
Write-LogMessage "Cleaning up component store..." "Yellow"
$cleanupCommands = @(
    @{Command = "dism /online /cleanup-image /startcomponentcleanup"; Description = "Component cleanup"},
    @{Command = "dism /online /cleanup-image /startcomponentcleanup /resetbase"; Description = "Component cleanup with reset base"},
    @{Command = "dism /online /cleanup-image /spsuperseded"; Description = "Service pack cleanup"}
)

foreach ($cmd in $cleanupCommands) {
    Invoke-CommandWithProgress $cmd.Command $cmd.Description 15
}

Write-LogMessage "=== PHASE 5: WINDOWS UPDATE COMPONENT REPAIR ===" "Cyan"

# Stop Windows Update services
$wuServices = @("wuauserv", "cryptSvc", "bits", "msiserver")
Write-LogMessage "Stopping Windows Update services..." "Yellow"
foreach ($service in $wuServices) {
    try {
        Stop-Service -Name $service -Force -ErrorAction SilentlyContinue
        Write-LogMessage "Stopped service: $service" "Cyan"
    } catch {
        Write-LogMessage "Could not stop service: $service" "Yellow"
    }
}

# Clear Windows Update cache
Write-LogMessage "Clearing Windows Update cache..." "Yellow"
$wuCachePaths = @(
    "$env:WINDIR\SoftwareDistribution\Download",
    "$env:WINDIR\System32\catroot2"
)

foreach ($path in $wuCachePaths) {
    try {
        if (Test-Path $path) {
            Remove-Item "$path\*" -Recurse -Force -ErrorAction SilentlyContinue
            Write-LogMessage "Cleared cache: $path" "Green"
        }
    } catch {
        Write-LogMessage "Could not clear cache: $path" "Yellow"
    }
}

# Restart Windows Update services
Write-LogMessage "Restarting Windows Update services..." "Yellow"
foreach ($service in $wuServices) {
    try {
        Start-Service -Name $service -ErrorAction SilentlyContinue
        Write-LogMessage "Started service: $service" "Green"
    } catch {
        Write-LogMessage "Could not start service: $service" "Yellow"
    }
}

Write-LogMessage "=== PHASE 6: APPX PROVISIONING REPAIR ===" "Cyan"

# Re-provision AppX packages
Write-LogMessage "Re-provisioning AppX packages..." "Yellow"
try {
    $provisionedPackages = Get-AppxProvisionedPackage -Online | Where-Object {$_.DisplayName -like "*Store*"}
    if ($provisionedPackages) {
        Write-LogMessage "Found $($provisionedPackages.Count) provisioned Store packages" "Cyan"
        foreach ($package in $provisionedPackages) {
            Write-LogMessage "Provisioned: $($package.DisplayName) v$($package.Version)" "White"
        }
    } else {
        Write-LogMessage "No provisioned Store packages found - attempting to add" "Yellow"
        
        # Try to add Store provisioning
        $storePackagePath = Get-ChildItem "$env:WINDIR\SystemApps" | Where-Object {$_.Name -like "*Store*"}
        if ($storePackagePath) {
            foreach ($path in $storePackagePath) {
                $manifestPath = Join-Path $path.FullName "AppxManifest.xml"
                if (Test-Path $manifestPath) {
                    try {
                        Add-AppxProvisionedPackage -Online -PackagePath $path.FullName -SkipLicense -ErrorAction SilentlyContinue
                        Write-LogMessage "Added provisioning for: $($path.Name)" "Green"
                    } catch {
                        Write-LogMessage "Could not provision: $($path.Name)" "Yellow"
                    }
                }
            }
        }
    }
} catch {
    Write-LogMessage "Error during AppX provisioning: $($_.Exception.Message)" "Red"
}

Write-LogMessage "=== PHASE 7: FINAL SYSTEM VERIFICATION ===" "Cyan"

# Verify system integrity
Write-LogMessage "Performing final system integrity check..." "Yellow"
$finalSfcSuccess = Invoke-CommandWithProgress "sfc /verifyonly" "Final SFC verification" 10

# Check if Store is now available
Write-LogMessage "Checking Microsoft Store availability..." "Yellow"
$storeCheck = Get-AppxPackage Microsoft.WindowsStore*
if ($storeCheck) {
    Write-LogMessage "SUCCESS: Microsoft Store found after system repair!" "Green"
    $storeCheck | ForEach-Object {
        Write-LogMessage "  - $($_.Name) v$($_.Version)" "Green"
    }
} else {
    Write-LogMessage "Microsoft Store still not found after system repair" "Red"
}

# Generate system health report
Write-LogMessage "Generating system health report..." "Yellow"
try {
    $healthReport = @{
        "SFC Scan" = if ($sfcSuccess) { "PASSED" } else { "FAILED" }
        "DISM Check" = if ($dismCheckSuccess) { "PASSED" } else { "FAILED" }
        "DISM Scan" = if ($dismScanSuccess) { "PASSED" } else { "FAILED" }
        "DISM Restore" = if ($dismRestoreSuccess) { "PASSED" } else { "FAILED" }
        "Final SFC" = if ($finalSfcSuccess) { "PASSED" } else { "FAILED" }
        "Store Found" = if ($storeCheck) { "YES" } else { "NO" }
    }
    
    Write-LogMessage "=== SYSTEM HEALTH REPORT ===" "Cyan"
    foreach ($check in $healthReport.Keys) {
        $status = $healthReport[$check]
        $color = if ($status -eq "PASSED" -or $status -eq "YES") { "Green" } else { "Red" }
        Write-LogMessage "$check : $status" $color
    }
} catch {
    Write-LogMessage "Could not generate health report: $($_.Exception.Message)" "Yellow"
}

# DEEP METHOD 4: Manual Microsoft Store Package Deployment
# This script downloads and manually deploys Microsoft Store APPX packages
# MUST BE RUN AS ADMINISTRATOR

Write-LogMessage "=== DEEP METHOD 4: MANUAL MICROSOFT STORE PACKAGE DEPLOYMENT ===" "Cyan"

# Create download directory
$downloadDir = ".\StorePackages"
if (-not (Test-Path $downloadDir)) {
    New-Item -ItemType Directory -Path $downloadDir -Force | Out-Null
    Write-LogMessage "Created download directory: $downloadDir" "Green"
}

# Microsoft Store package URLs (these are official Microsoft links)
$storePackages = @{
    "Microsoft.WindowsStore" = "https://aka.ms/Microsoft.WindowsStore_Latest"
    "Microsoft.StorePurchaseApp" = "https://aka.ms/Microsoft.StorePurchaseApp_Latest"
    "Microsoft.DesktopAppInstaller" = "https://aka.ms/Microsoft.DesktopAppInstaller_Latest"
}

Write-LogMessage "Attempting to download Microsoft Store packages..." "Yellow"

foreach ($packageName in $storePackages.Keys) {
    $url = $storePackages[$packageName]
    $outputPath = Join-Path $downloadDir "$packageName.appx"

    Write-LogMessage "Downloading: $packageName" "Yellow"
    try {
        # Try to download using different methods
        $downloadSuccess = $false

        # Method 1: Invoke-WebRequest
        try {
            Invoke-WebRequest -Uri $url -OutFile $outputPath -UseBasicParsing -ErrorAction Stop
            $downloadSuccess = $true
            Write-LogMessage "Downloaded successfully: $packageName" "Green"
        } catch {
            Write-LogMessage "WebRequest failed for $packageName : $($_.Exception.Message)" "Yellow"
        }

        # Method 2: System.Net.WebClient (if WebRequest failed)
        if (-not $downloadSuccess) {
            try {
                $webClient = New-Object System.Net.WebClient
                $webClient.DownloadFile($url, $outputPath)
                $downloadSuccess = $true
                Write-LogMessage "Downloaded via WebClient: $packageName" "Green"
            } catch {
                Write-LogMessage "WebClient failed for $packageName : $($_.Exception.Message)" "Yellow"
            }
        }

        # Method 3: BITS transfer (if others failed)
        if (-not $downloadSuccess) {
            try {
                Start-BitsTransfer -Source $url -Destination $outputPath -ErrorAction Stop
                $downloadSuccess = $true
                Write-LogMessage "Downloaded via BITS: $packageName" "Green"
            } catch {
                Write-LogMessage "BITS failed for $packageName : $($_.Exception.Message)" "Yellow"
            }
        }

        if (-not $downloadSuccess) {
            Write-LogMessage "All download methods failed for: $packageName" "Red"
        }

    } catch {
        Write-LogMessage "Error downloading $packageName : $($_.Exception.Message)" "Red"
    }
}

# Install downloaded packages
Write-LogMessage "Installing downloaded Microsoft Store packages..." "Yellow"
$downloadedPackages = Get-ChildItem $downloadDir -Filter "*.appx" -ErrorAction SilentlyContinue

if ($downloadedPackages) {
    foreach ($package in $downloadedPackages) {
        Write-LogMessage "Installing package: $($package.Name)" "Yellow"
        try {
            Add-AppxPackage -Path $package.FullName -ForceApplicationShutdown -ErrorAction Stop
            Write-LogMessage "Successfully installed: $($package.Name)" "Green"
        } catch {
            Write-LogMessage "Failed to install $($package.Name) : $($_.Exception.Message)" "Red"

            # Try alternative installation method
            try {
                Add-AppxPackage -Path $package.FullName -ForceUpdateFromAnyVersion -ErrorAction Stop
                Write-LogMessage "Installed with force update: $($package.Name)" "Green"
            } catch {
                Write-LogMessage "Alternative installation also failed for: $($package.Name)" "Red"
            }
        }
    }
} else {
    Write-LogMessage "No packages were downloaded successfully" "Red"

    # Try alternative: Use Windows Package Manager (winget)
    Write-LogMessage "Attempting installation via Windows Package Manager..." "Yellow"
    try {
        $wingetResult = winget install "Microsoft Store" --source msstore --accept-package-agreements --accept-source-agreements
        if ($LASTEXITCODE -eq 0) {
            Write-LogMessage "Microsoft Store installed via winget" "Green"
        } else {
            Write-LogMessage "Winget installation failed (Exit: $LASTEXITCODE)" "Red"
        }
    } catch {
        Write-LogMessage "Winget installation error: $($_.Exception.Message)" "Red"
    }
}

Write-LogMessage "=== SYSTEM REPAIR COMPLETED ===" "Cyan"
Write-LogMessage "Deep system file integrity and component store repair finished" "Green"
Write-LogMessage "RECOMMENDATION: Restart computer for all changes to take effect" "Yellow"

Read-Host "Press Enter to continue"

# Direct Microsoft Store Installation
# This script tries multiple direct installation methods

Write-Host "================================================================" -ForegroundColor Cyan
Write-Host "    DIRECT MICROSOFT STORE INSTALLATION" -ForegroundColor Cyan
Write-Host "================================================================" -ForegroundColor Cyan

function Write-LogMessage {
    param([string]$Message, [string]$Color = "White")
    $timestamp = Get-Date -Format "HH:mm:ss"
    Write-Host "[$timestamp] $Message" -ForegroundColor $Color
}

# Method 1: Try to install from Microsoft's official download
Write-LogMessage "Method 1: Attempting direct download installation..." "Yellow"

try {
    # Create download directory
    $downloadDir = ".\MSStoreDownload"
    if (-not (Test-Path $downloadDir)) {
        New-Item -ItemType Directory -Path $downloadDir -Force | Out-Null
    }
    
    # Try to download Microsoft Store installer
    Write-LogMessage "Attempting to download Microsoft Store installer..." "Cyan"
    
    # Use different download methods
    $downloadSuccess = $false
    
    # Method 1a: Try PowerShell download
    try {
        $url = "https://aka.ms/getwinget"  # This often redirects to App Installer which includes Store
        $outputPath = "$downloadDir\AppInstaller.msixbundle"
        Invoke-WebRequest -Uri $url -OutFile $outputPath -UseBasicParsing -ErrorAction Stop
        Write-LogMessage "Downloaded App Installer bundle" "Green"
        $downloadSuccess = $true
    } catch {
        Write-LogMessage "PowerShell download failed: $($_.Exception.Message)" "Yellow"
    }
    
    # Method 1b: Try BITS transfer
    if (-not $downloadSuccess) {
        try {
            $url = "https://aka.ms/getwinget"
            $outputPath = "$downloadDir\AppInstaller.msixbundle"
            Start-BitsTransfer -Source $url -Destination $outputPath -ErrorAction Stop
            Write-LogMessage "Downloaded via BITS transfer" "Green"
            $downloadSuccess = $true
        } catch {
            Write-LogMessage "BITS download failed: $($_.Exception.Message)" "Yellow"
        }
    }
    
    # Install downloaded package
    if ($downloadSuccess -and (Test-Path "$downloadDir\AppInstaller.msixbundle")) {
        Write-LogMessage "Installing downloaded package..." "Cyan"
        try {
            Add-AppxPackage -Path "$downloadDir\AppInstaller.msixbundle" -ErrorAction Stop
            Write-LogMessage "Package installation completed" "Green"
        } catch {
            Write-LogMessage "Package installation failed: $($_.Exception.Message)" "Red"
        }
    }
    
} catch {
    Write-LogMessage "Direct download method failed: $($_.Exception.Message)" "Red"
}

# Method 2: Try to enable Microsoft Store via Windows Features
Write-LogMessage "Method 2: Enabling Microsoft Store via Windows Features..." "Yellow"
try {
    # Try to enable Windows Store feature
    $featureResult = Enable-WindowsOptionalFeature -Online -FeatureName "Microsoft-Windows-Store" -All -NoRestart -ErrorAction SilentlyContinue
    if ($featureResult) {
        Write-LogMessage "Windows Store feature enabled" "Green"
    } else {
        Write-LogMessage "Windows Store feature not available or already enabled" "Yellow"
    }
} catch {
    Write-LogMessage "Windows Features method failed: $($_.Exception.Message)" "Red"
}

# Method 3: Try to install via Microsoft Store URL protocol
Write-LogMessage "Method 3: Testing Microsoft Store URL protocol..." "Yellow"
try {
    # Try to open Microsoft Store
    Start-Process "ms-windows-store:" -ErrorAction Stop
    Write-LogMessage "Microsoft Store protocol responded" "Green"
} catch {
    Write-LogMessage "Microsoft Store protocol failed: $($_.Exception.Message)" "Red"
}

# Method 4: Reset Windows Store components
Write-LogMessage "Method 4: Resetting Windows Store components..." "Yellow"
try {
    # Reset Windows Store
    Start-Process "wsreset.exe" -Wait -WindowStyle Hidden -ErrorAction SilentlyContinue
    Write-LogMessage "Windows Store reset completed" "Green"
} catch {
    Write-LogMessage "Windows Store reset failed: $($_.Exception.Message)" "Red"
}

# Method 5: Check for system apps
Write-LogMessage "Method 5: Checking for Microsoft Store in system apps..." "Yellow"
try {
    $systemApps = Get-ChildItem "$env:WINDIR\SystemApps" | Where-Object {$_.Name -like "*Store*"}
    if ($systemApps) {
        Write-LogMessage "Found Store system apps:" "Green"
        $systemApps | ForEach-Object {
            Write-LogMessage "  - $($_.Name)" "Cyan"
            
            # Try to register the system app
            $manifestPath = Join-Path $_.FullName "AppxManifest.xml"
            if (Test-Path $manifestPath) {
                try {
                    Add-AppxPackage -Register $manifestPath -DisableDevelopmentMode -ErrorAction SilentlyContinue
                    Write-LogMessage "Registered system app: $($_.Name)" "Green"
                } catch {
                    Write-LogMessage "Failed to register: $($_.Name)" "Yellow"
                }
            }
        }
    } else {
        Write-LogMessage "No Store system apps found" "Yellow"
    }
} catch {
    Write-LogMessage "System apps check failed: $($_.Exception.Message)" "Red"
}

# Final verification
Write-LogMessage "Performing final verification..." "Yellow"
Start-Sleep -Seconds 3

$finalStoreCheck = Get-AppxPackage Microsoft.WindowsStore*
$finalPurchaseCheck = Get-AppxPackage Microsoft.StorePurchaseApp*

Write-LogMessage "=== INSTALLATION RESULTS ===" "Cyan"

if ($finalStoreCheck) {
    Write-LogMessage "✓ Microsoft Store: INSTALLED" "Green"
    $finalStoreCheck | ForEach-Object {
        Write-LogMessage "  - $($_.Name) v$($_.Version)" "Green"
    }
} else {
    Write-LogMessage "✗ Microsoft Store: NOT FOUND" "Red"
}

if ($finalPurchaseCheck) {
    Write-LogMessage "✓ Store Purchase App: INSTALLED" "Green"
    $finalPurchaseCheck | ForEach-Object {
        Write-LogMessage "  - $($_.Name) v$($_.Version)" "Green"
    }
} else {
    Write-LogMessage "✗ Store Purchase App: NOT FOUND" "Red"
}

# Test store functionality
Write-LogMessage "Testing store functionality..." "Yellow"
try {
    Start-Process "ms-windows-store:" -ErrorAction Stop
    Write-LogMessage "✓ Store protocol: WORKING" "Green"
} catch {
    Write-LogMessage "✗ Store protocol: FAILED" "Red"
}

if ($finalStoreCheck -or $finalPurchaseCheck) {
    Write-LogMessage "================================================================" "Green"
    Write-LogMessage "INSTALLATION SUCCESSFUL!" "Green"
    Write-LogMessage "================================================================" "Green"
    Write-LogMessage "Microsoft Store components have been installed" "Green"
    Write-LogMessage "Please restart your computer to complete the installation" "Yellow"
} else {
    Write-LogMessage "================================================================" "Red"
    Write-LogMessage "INSTALLATION INCOMPLETE" "Red"
    Write-LogMessage "================================================================" "Red"
    Write-LogMessage "Microsoft Store was not successfully installed" "Red"
    Write-LogMessage "You may need to:" "Yellow"
    Write-LogMessage "1. Run Windows Update" "Yellow"
    Write-LogMessage "2. Check Windows Settings > Apps > Optional Features" "Yellow"
    Write-LogMessage "3. Contact Microsoft Support" "Yellow"
}

Write-LogMessage "Direct installation script completed." "Cyan"
Read-Host "Press Enter to exit"

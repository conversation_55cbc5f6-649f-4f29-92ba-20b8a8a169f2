# DEEP METHOD 1: Complete Windows Store Ecosystem Repair
# This script performs comprehensive Microsoft Store repair at multiple system levels
# MUST BE RUN AS ADMINISTRATOR

param(
    [switch]$SkipReboot,
    [switch]$Verbose
)

$ErrorActionPreference = "Continue"
$VerbosePreference = if ($Verbose) { "Continue" } else { "SilentlyContinue" }

Write-Host "=== DEEP MICROSOFT STORE ECOSYSTEM REPAIR ===" -ForegroundColor Cyan
Write-Host "This script will perform comprehensive Microsoft Store repair" -ForegroundColor Yellow
Write-Host "Estimated time: 15-30 minutes" -ForegroundColor Yellow

# Check administrator privileges
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "CRITICAL ERROR: Administrator privileges required!" -ForegroundColor Red
    Write-Host "Please run PowerShell as Administrator and try again." -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "Administrator privileges confirmed." -ForegroundColor Green

# Function to log with timestamp
function Write-LogMessage {
    param([string]$Message, [string]$Color = "White")
    $timestamp = Get-Date -Format "HH:mm:ss"
    Write-Host "[$timestamp] $Message" -ForegroundColor $Color
}

# Function to run command with error handling
function Invoke-SafeCommand {
    param([string]$Command, [string]$Description)
    Write-LogMessage "Starting: $Description" "Yellow"
    try {
        $result = Invoke-Expression $Command
        Write-LogMessage "Completed: $Description" "Green"
        return $result
    } catch {
        Write-LogMessage "Failed: $Description - $($_.Exception.Message)" "Red"
        return $null
    }
}

Write-LogMessage "=== PHASE 1: SYSTEM ANALYSIS ===" "Cyan"

# Analyze current system state
Write-LogMessage "Analyzing Windows version and build..."
$osInfo = Get-ComputerInfo | Select-Object WindowsProductName, WindowsVersion, WindowsBuildLabEx
Write-LogMessage "OS: $($osInfo.WindowsProductName) $($osInfo.WindowsVersion)" "White"
Write-LogMessage "Build: $($osInfo.WindowsBuildLabEx)" "White"

# Check current store packages
Write-LogMessage "Checking current Microsoft Store packages..."
$currentStorePackages = Get-AppxPackage | Where-Object {$_.Name -like "*Store*"}
if ($currentStorePackages) {
    $currentStorePackages | ForEach-Object {
        Write-LogMessage "Found: $($_.Name) v$($_.Version) [$($_.Status)]" "Cyan"
    }
} else {
    Write-LogMessage "No Microsoft Store packages found for current user" "Red"
}

# Check all users store packages
Write-LogMessage "Checking Microsoft Store packages for all users..."
try {
    $allUsersStorePackages = Get-AppxPackage -AllUsers | Where-Object {$_.Name -like "*Store*"}
    if ($allUsersStorePackages) {
        $allUsersStorePackages | ForEach-Object {
            Write-LogMessage "All Users: $($_.Name) v$($_.Version)" "Cyan"
        }
    } else {
        Write-LogMessage "No Microsoft Store packages found for any user" "Red"
    }
} catch {
    Write-LogMessage "Cannot check all users packages: $($_.Exception.Message)" "Yellow"
}

Write-LogMessage "=== PHASE 2: WINDOWS STORE SERVICES REPAIR ===" "Cyan"

# Stop and restart Windows Store related services
$storeServices = @(
    "AppXSvc",           # AppX Deployment Service
    "ClipSVC",           # Client License Service
    "LicenseManager",    # Windows License Manager Service
    "WSService",         # Windows Store Service
    "StorSvc",           # Storage Service
    "InstallService"     # Microsoft Store Install Service
)

foreach ($service in $storeServices) {
    Write-LogMessage "Processing service: $service"
    try {
        $svc = Get-Service -Name $service -ErrorAction SilentlyContinue
        if ($svc) {
            if ($svc.Status -eq "Running") {
                Write-LogMessage "Stopping service: $service" "Yellow"
                Stop-Service -Name $service -Force -ErrorAction SilentlyContinue
                Start-Sleep -Seconds 2
            }
            Write-LogMessage "Starting service: $service" "Yellow"
            Start-Service -Name $service -ErrorAction SilentlyContinue
            Write-LogMessage "Service $service restarted successfully" "Green"
        } else {
            Write-LogMessage "Service $service not found" "Yellow"
        }
    } catch {
        Write-LogMessage "Error with service $service : $($_.Exception.Message)" "Red"
    }
}

Write-LogMessage "=== PHASE 3: APPX PACKAGE SYSTEM REPAIR ===" "Cyan"

# Reset AppX package system
Write-LogMessage "Resetting AppX package deployment system..."
Invoke-SafeCommand "Get-AppxPackage -AllUsers | Where-Object {`$_.Name -like '*Store*'} | Remove-AppxPackage -AllUsers -ErrorAction SilentlyContinue" "Remove existing Store packages"

# Clear AppX cache
Write-LogMessage "Clearing AppX cache and temporary files..."
$appxCachePaths = @(
    "$env:LOCALAPPDATA\Packages\Microsoft.WindowsStore_*",
    "$env:LOCALAPPDATA\Microsoft\Windows\INetCache\*",
    "$env:TEMP\*Store*",
    "$env:WINDIR\Temp\*Store*"
)

foreach ($path in $appxCachePaths) {
    try {
        $items = Get-ChildItem $path -ErrorAction SilentlyContinue
        if ($items) {
            Write-LogMessage "Clearing cache: $path"
            Remove-Item $path -Recurse -Force -ErrorAction SilentlyContinue
        }
    } catch {
        Write-LogMessage "Could not clear cache: $path" "Yellow"
    }
}

Write-LogMessage "=== PHASE 4: WINDOWS CAPABILITIES RESTORATION ===" "Cyan"

# Check and install Windows capabilities
Write-LogMessage "Checking Windows capabilities..."
try {
    $capabilities = Get-WindowsCapability -Online | Where-Object {$_.Name -like "*Store*"}
    if ($capabilities) {
        $capabilities | ForEach-Object {
            Write-LogMessage "Capability: $($_.Name) - State: $($_.State)" "Cyan"
            if ($_.State -ne "Installed") {
                Write-LogMessage "Installing capability: $($_.Name)" "Yellow"
                Add-WindowsCapability -Online -Name $_.Name -ErrorAction SilentlyContinue
            }
        }
    }
} catch {
    Write-LogMessage "Error checking capabilities: $($_.Exception.Message)" "Red"
}

# Try multiple DISM approaches
$dismCapabilities = @(
    "Microsoft.Windows.Store~~~~*******",
    "Microsoft.WindowsStore~~~~*******",
    "Microsoft.Store~~~~*******"
)

foreach ($capability in $dismCapabilities) {
    Write-LogMessage "Attempting DISM installation: $capability" "Yellow"
    try {
        $dismResult = dism /online /add-capability /capabilityname:$capability /quiet
        if ($LASTEXITCODE -eq 0) {
            Write-LogMessage "DISM success: $capability" "Green"
        } else {
            Write-LogMessage "DISM failed: $capability (Exit: $LASTEXITCODE)" "Yellow"
        }
    } catch {
        Write-LogMessage "DISM error: $capability - $($_.Exception.Message)" "Red"
    }
}

Write-LogMessage "=== PHASE 5: COMPONENT STORE REPAIR ===" "Cyan"

# System file checker
Write-LogMessage "Running System File Checker (SFC)..." "Yellow"
Invoke-SafeCommand "sfc /scannow" "System File Checker scan"

# DISM component store repair
Write-LogMessage "Running DISM component store repair..." "Yellow"
Invoke-SafeCommand "dism /online /cleanup-image /restorehealth" "DISM component store repair"

Write-LogMessage "=== PHASE 6: FINAL VERIFICATION ===" "Cyan"

# Reset Windows Store cache
Write-LogMessage "Resetting Windows Store cache..." "Yellow"
try {
    Start-Process "wsreset.exe" -Wait -WindowStyle Hidden -ErrorAction SilentlyContinue
    Write-LogMessage "Windows Store cache reset completed" "Green"
} catch {
    Write-LogMessage "Windows Store cache reset failed: $($_.Exception.Message)" "Red"
}

# Final verification
Write-LogMessage "Performing final verification..." "Yellow"
$finalStoreCheck = Get-AppxPackage Microsoft.WindowsStore*
if ($finalStoreCheck) {
    Write-LogMessage "SUCCESS: Microsoft Store packages found!" "Green"
    $finalStoreCheck | ForEach-Object {
        Write-LogMessage "  - $($_.Name) v$($_.Version)" "Green"
    }
    
    # Test store launch
    try {
        Start-Process "ms-windows-store:" -ErrorAction Stop
        Write-LogMessage "Microsoft Store launched successfully!" "Green"
    } catch {
        Write-LogMessage "Store launch test failed: $($_.Exception.Message)" "Yellow"
    }
} else {
    Write-LogMessage "Microsoft Store still not found after deep repair" "Red"
    Write-LogMessage "Manual intervention may be required" "Yellow"
}

Write-LogMessage "=== DEEP REPAIR COMPLETED ===" "Cyan"
if (-not $SkipReboot) {
    Write-LogMessage "RECOMMENDATION: Restart your computer for all changes to take effect" "Yellow"
    $reboot = Read-Host "Would you like to restart now? (y/n)"
    if ($reboot -eq "y" -or $reboot -eq "Y") {
        Write-LogMessage "Restarting computer..." "Green"
        Restart-Computer -Force
    }
} else {
    Write-LogMessage "Skipping reboot as requested" "Yellow"
}

Write-LogMessage "Script completed. Check results above." "Cyan"
Read-Host "Press Enter to exit"

# Microsoft Store Reinstallation Script
# This script must be run as Administrator

Write-Host "Checking current Microsoft Store installation..." -ForegroundColor Yellow

# Check if Microsoft Store is installed
$storePackages = Get-AppxPackage -AllUsers Microsoft.WindowsStore*

if ($storePackages) {
    Write-Host "Found Microsoft Store packages:" -ForegroundColor Green
    $storePackages | ForEach-Object {
        Write-Host "  - $($_.Name) (Version: $($_.Version))" -ForegroundColor Cyan
    }
    
    Write-Host "`nReinstalling Microsoft Store..." -ForegroundColor Yellow
    
    try {
        $storePackages | ForEach-Object {
            $manifestPath = "$($_.InstallLocation)\AppXManifest.xml"
            if (Test-Path $manifestPath) {
                Write-Host "Registering: $manifestPath" -ForegroundColor Cyan
                Add-AppxPackage -DisableDevelopmentMode -Register $manifestPath
            } else {
                Write-Host "Manifest not found: $manifestPath" -ForegroundColor Red
            }
        }
        Write-Host "`nMicrosoft Store reinstallation completed successfully!" -ForegroundColor Green
    } catch {
        Write-Host "Error during reinstallation: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "No Microsoft Store packages found. Attempting to install from Windows features..." -ForegroundColor Yellow
    
    try {
        # Try to install Microsoft Store using DISM
        Write-Host "Installing Microsoft Store using DISM..." -ForegroundColor Cyan
        dism /online /add-capability /capabilityname:Microsoft.Windows.Store~~~~*******
        Write-Host "Microsoft Store installation completed!" -ForegroundColor Green
    } catch {
        Write-Host "Error installing Microsoft Store: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`nResetting Microsoft Store cache..." -ForegroundColor Yellow
Start-Process "wsreset.exe" -Wait

Write-Host "`nScript completed. Please restart your computer for changes to take effect." -ForegroundColor Green
Write-Host "Press any key to continue..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

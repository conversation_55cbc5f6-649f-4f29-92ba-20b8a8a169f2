# Microsoft Store Complete Reinstallation Script
# This script must be run as Administrator

Write-Host "=== Microsoft Store Complete Reinstallation ===" -ForegroundColor Cyan
Write-Host "Checking administrator privileges..." -ForegroundColor Yellow

# Check if running as administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "ERROR: This script must be run as Administrator!" -ForegroundColor Red
    Write-Host "Please right-click PowerShell and select 'Run as Administrator'" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "Administrator privileges confirmed." -ForegroundColor Green

# Step 1: Check current installation
Write-Host "`n=== Step 1: Checking current Microsoft Store installation ===" -ForegroundColor Cyan
$currentUserStore = Get-AppxPackage Microsoft.WindowsStore*
$allUsersStore = Get-AppxPackage -AllUsers Microsoft.WindowsStore*

Write-Host "Current user packages:" -ForegroundColor Yellow
if ($currentUserStore) {
    $currentUserStore | ForEach-Object { Write-Host "  - $($_.Name) v$($_.Version)" -ForegroundColor Green }
} else {
    Write-Host "  - No Microsoft Store found for current user" -ForegroundColor Red
}

Write-Host "All users packages:" -ForegroundColor Yellow
if ($allUsersStore) {
    $allUsersStore | ForEach-Object { Write-Host "  - $($_.Name) v$($_.Version)" -ForegroundColor Green }
} else {
    Write-Host "  - No Microsoft Store found for any user" -ForegroundColor Red
}

# Step 2: Remove existing installations
Write-Host "`n=== Step 2: Removing existing Microsoft Store installations ===" -ForegroundColor Cyan
try {
    if ($currentUserStore) {
        Write-Host "Removing Microsoft Store for current user..." -ForegroundColor Yellow
        $currentUserStore | Remove-AppxPackage -ErrorAction SilentlyContinue
    }

    if ($allUsersStore) {
        Write-Host "Removing Microsoft Store for all users..." -ForegroundColor Yellow
        $allUsersStore | Remove-AppxPackage -AllUsers -ErrorAction SilentlyContinue
    }
    Write-Host "Removal completed." -ForegroundColor Green
} catch {
    Write-Host "Warning during removal: $($_.Exception.Message)" -ForegroundColor Yellow
}

# Step 3: Install using DISM
Write-Host "`n=== Step 3: Installing Microsoft Store using DISM ===" -ForegroundColor Cyan
try {
    Write-Host "Installing Microsoft Store capability..." -ForegroundColor Yellow
    $dismResult = dism /online /add-capability /capabilityname:Microsoft.Windows.Store~~~~*******
    Write-Host "DISM installation completed." -ForegroundColor Green
} catch {
    Write-Host "DISM installation failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Step 4: Try PowerShell method
Write-Host "`n=== Step 4: Installing using PowerShell method ===" -ForegroundColor Cyan
try {
    Write-Host "Attempting PowerShell installation..." -ForegroundColor Yellow
    Get-AppxPackage -AllUsers Microsoft.WindowsStore* | Foreach {Add-AppxPackage -DisableDevelopmentMode -Register "$($_.InstallLocation)\AppXManifest.xml"}
    Write-Host "PowerShell installation completed." -ForegroundColor Green
} catch {
    Write-Host "PowerShell installation failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Step 5: Reset Windows Store
Write-Host "`n=== Step 5: Resetting Windows Store cache ===" -ForegroundColor Cyan
try {
    Write-Host "Running WSReset..." -ForegroundColor Yellow
    Start-Process "wsreset.exe" -Wait -WindowStyle Hidden
    Write-Host "WSReset completed." -ForegroundColor Green
} catch {
    Write-Host "WSReset failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Step 6: Verify installation
Write-Host "`n=== Step 6: Verifying installation ===" -ForegroundColor Cyan
$finalCheck = Get-AppxPackage Microsoft.WindowsStore*
if ($finalCheck) {
    Write-Host "SUCCESS: Microsoft Store is now installed!" -ForegroundColor Green
    $finalCheck | ForEach-Object { Write-Host "  - $($_.Name) v$($_.Version)" -ForegroundColor Green }

    # Try to launch the store
    Write-Host "`nTesting Microsoft Store launch..." -ForegroundColor Yellow
    try {
        Start-Process "ms-windows-store:" -ErrorAction Stop
        Write-Host "Microsoft Store launched successfully!" -ForegroundColor Green
    } catch {
        Write-Host "Failed to launch Microsoft Store: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "FAILED: Microsoft Store is still not installed." -ForegroundColor Red
    Write-Host "You may need to:" -ForegroundColor Yellow
    Write-Host "1. Run Windows Update" -ForegroundColor Yellow
    Write-Host "2. Use Windows Settings > Apps > Optional Features > Add Feature" -ForegroundColor Yellow
    Write-Host "3. Reset Windows 11 (keeping files)" -ForegroundColor Yellow
}

Write-Host "`n=== Script completed ===" -ForegroundColor Cyan
Write-Host "Please restart your computer for all changes to take effect." -ForegroundColor Green
Write-Host "Press any key to continue..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

# DEEP METHOD 2: Registry-level Microsoft Store Restoration
# This script performs deep registry analysis and repair for Microsoft Store
# MUST BE RUN AS ADMINISTRATOR

param(
    [switch]$BackupRegistry,
    [switch]$Verbose
)

$ErrorActionPreference = "Continue"
$VerbosePreference = if ($Verbose) { "Continue" } else { "SilentlyContinue" }

Write-Host "=== DEEP REGISTRY-LEVEL MICROSOFT STORE RESTORATION ===" -ForegroundColor Cyan
Write-Host "This script will analyze and repair Microsoft Store registry entries" -ForegroundColor Yellow
Write-Host "WARNING: This modifies system registry - backup recommended" -ForegroundColor Red

# Check administrator privileges
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "CRITICAL ERROR: Administrator privileges required!" -ForegroundColor Red
    exit 1
}

function Write-LogMessage {
    param([string]$Message, [string]$Color = "White")
    $timestamp = Get-Date -Format "HH:mm:ss"
    Write-Host "[$timestamp] $Message" -ForegroundColor $Color
}

# Create registry backup if requested
if ($BackupRegistry) {
    Write-LogMessage "Creating registry backup..." "Yellow"
    $backupPath = ".\registry_backup_$(Get-Date -Format 'yyyyMMdd_HHmmss').reg"
    try {
        reg export "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Appx" $backupPath /y
        Write-LogMessage "Registry backup created: $backupPath" "Green"
    } catch {
        Write-LogMessage "Registry backup failed: $($_.Exception.Message)" "Red"
    }
}

Write-LogMessage "=== PHASE 1: REGISTRY ANALYSIS ===" "Cyan"

# Define critical registry paths for Microsoft Store
$registryPaths = @{
    "AppX Deployment" = "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Appx"
    "Store Policies" = "HKLM:\SOFTWARE\Policies\Microsoft\WindowsStore"
    "Store Configuration" = "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Store"
    "Package Management" = "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\PackageManagement"
    "App Repository" = "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\AppRepository"
    "User Store Settings" = "HKCU:\SOFTWARE\Microsoft\Windows\CurrentVersion\Store"
    "User AppX" = "HKCU:\SOFTWARE\Classes\Local Settings\Software\Microsoft\Windows\CurrentVersion\AppModel"
}

# Analyze current registry state
foreach ($pathName in $registryPaths.Keys) {
    $path = $registryPaths[$pathName]
    Write-LogMessage "Analyzing: $pathName" "Yellow"
    
    if (Test-Path $path) {
        try {
            $items = Get-ChildItem $path -ErrorAction SilentlyContinue
            Write-LogMessage "  Found $($items.Count) entries in $pathName" "Green"
            
            # Check for Store-specific entries
            $storeEntries = $items | Where-Object {$_.Name -like "*Store*"}
            if ($storeEntries) {
                Write-LogMessage "  Store-related entries: $($storeEntries.Count)" "Cyan"
            } else {
                Write-LogMessage "  No Store-related entries found" "Yellow"
            }
        } catch {
            Write-LogMessage "  Error reading $pathName : $($_.Exception.Message)" "Red"
        }
    } else {
        Write-LogMessage "  Registry path not found: $pathName" "Red"
    }
}

Write-LogMessage "=== PHASE 2: REGISTRY REPAIR ===" "Cyan"

# Remove problematic Store policies that might block installation
Write-LogMessage "Removing restrictive Store policies..." "Yellow"
$storePolicyPath = "HKLM:\SOFTWARE\Policies\Microsoft\WindowsStore"
if (Test-Path $storePolicyPath) {
    try {
        $policies = Get-ItemProperty $storePolicyPath -ErrorAction SilentlyContinue
        if ($policies.DisableStoreApps -eq 1) {
            Write-LogMessage "Removing DisableStoreApps policy" "Yellow"
            Remove-ItemProperty -Path $storePolicyPath -Name "DisableStoreApps" -ErrorAction SilentlyContinue
        }
        if ($policies.RequirePrivateStoreOnly -eq 1) {
            Write-LogMessage "Removing RequirePrivateStoreOnly policy" "Yellow"
            Remove-ItemProperty -Path $storePolicyPath -Name "RequirePrivateStoreOnly" -ErrorAction SilentlyContinue
        }
    } catch {
        Write-LogMessage "Error removing Store policies: $($_.Exception.Message)" "Red"
    }
}

# Reset AppX deployment settings
Write-LogMessage "Resetting AppX deployment settings..." "Yellow"
$appxPath = "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Appx"
if (Test-Path $appxPath) {
    try {
        # Reset deployment settings
        $deploymentPath = "$appxPath\AppxAllUserStore\Deprovisioned"
        if (Test-Path $deploymentPath) {
            $deprovisionedApps = Get-ChildItem $deploymentPath | Where-Object {$_.Name -like "*Store*"}
            foreach ($app in $deprovisionedApps) {
                Write-LogMessage "Removing deprovisioned entry: $($app.Name)" "Yellow"
                Remove-Item $app.PSPath -Recurse -Force -ErrorAction SilentlyContinue
            }
        }
    } catch {
        Write-LogMessage "Error resetting AppX deployment: $($_.Exception.Message)" "Red"
    }
}

# Repair user-specific Store settings
Write-LogMessage "Repairing user Store settings..." "Yellow"
$userStorePath = "HKCU:\SOFTWARE\Microsoft\Windows\CurrentVersion\Store"
try {
    if (Test-Path $userStorePath) {
        # Reset Store cache settings
        Remove-ItemProperty -Path $userStorePath -Name "Cache*" -ErrorAction SilentlyContinue
        Write-LogMessage "User Store cache settings reset" "Green"
    }
} catch {
    Write-LogMessage "Error repairing user Store settings: $($_.Exception.Message)" "Red"
}

Write-LogMessage "=== PHASE 3: COMPONENT REGISTRATION ===" "Cyan"

# Re-register Store components in registry
Write-LogMessage "Re-registering Store components..." "Yellow"

# Register Store application
$storeAppPath = "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Appx\AppxAllUserStore\Applications"
if (-not (Test-Path $storeAppPath)) {
    try {
        New-Item -Path $storeAppPath -Force -ErrorAction SilentlyContinue
        Write-LogMessage "Created Store applications registry path" "Green"
    } catch {
        Write-LogMessage "Failed to create Store applications path: $($_.Exception.Message)" "Red"
    }
}

# Register Store services in registry
$storeServices = @{
    "AppXSvc" = @{
        "DisplayName" = "AppX Deployment Service (AppXSVC)"
        "Description" = "Provides infrastructure support for deploying Store applications"
    }
    "ClipSVC" = @{
        "DisplayName" = "Client License Service (ClipSVC)"
        "Description" = "Provides infrastructure support for the Microsoft Store"
    }
}

foreach ($serviceName in $storeServices.Keys) {
    $servicePath = "HKLM:\SYSTEM\CurrentControlSet\Services\$serviceName"
    if (Test-Path $servicePath) {
        try {
            $serviceInfo = $storeServices[$serviceName]
            Set-ItemProperty -Path $servicePath -Name "DisplayName" -Value $serviceInfo.DisplayName -ErrorAction SilentlyContinue
            Set-ItemProperty -Path $servicePath -Name "Description" -Value $serviceInfo.Description -ErrorAction SilentlyContinue
            Write-LogMessage "Updated service registration: $serviceName" "Green"
        } catch {
            Write-LogMessage "Failed to update service $serviceName : $($_.Exception.Message)" "Red"
        }
    }
}

Write-LogMessage "=== PHASE 4: ADVANCED REGISTRY FIXES ===" "Cyan"

# Fix Windows Update components that affect Store
Write-LogMessage "Fixing Windows Update registry entries..." "Yellow"
$wuPath = "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\WindowsUpdate\Auto Update"
if (Test-Path $wuPath) {
    try {
        # Ensure Windows Update can install Store updates
        Set-ItemProperty -Path $wuPath -Name "IncludeRecommendedUpdates" -Value 1 -Type DWord -ErrorAction SilentlyContinue
        Write-LogMessage "Windows Update registry fixed" "Green"
    } catch {
        Write-LogMessage "Failed to fix Windows Update registry: $($_.Exception.Message)" "Red"
    }
}

# Fix licensing components
Write-LogMessage "Fixing licensing registry entries..." "Yellow"
$licensePath = "HKLM:\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Software Protection Platform"
if (Test-Path $licensePath) {
    try {
        # Reset licensing cache
        Remove-ItemProperty -Path $licensePath -Name "Cache*" -ErrorAction SilentlyContinue
        Write-LogMessage "Licensing cache reset" "Green"
    } catch {
        Write-LogMessage "Failed to reset licensing cache: $($_.Exception.Message)" "Red"
    }
}

# Fix package repository settings
Write-LogMessage "Fixing package repository settings..." "Yellow"
$repoPath = "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\AppRepository"
if (-not (Test-Path $repoPath)) {
    try {
        New-Item -Path $repoPath -Force -ErrorAction SilentlyContinue
        Write-LogMessage "Created package repository registry path" "Green"
    } catch {
        Write-LogMessage "Failed to create repository path: $($_.Exception.Message)" "Red"
    }
}

Write-LogMessage "=== PHASE 5: REGISTRY VERIFICATION ===" "Cyan"

# Verify critical registry entries exist
$criticalPaths = @(
    "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Appx",
    "HKLM:\SYSTEM\CurrentControlSet\Services\AppXSvc",
    "HKLM:\SYSTEM\CurrentControlSet\Services\ClipSVC"
)

foreach ($path in $criticalPaths) {
    if (Test-Path $path) {
        Write-LogMessage "Verified: $path" "Green"
    } else {
        Write-LogMessage "Missing: $path" "Red"
    }
}

# Force registry refresh
Write-LogMessage "Forcing registry refresh..." "Yellow"
try {
    # Refresh group policy
    gpupdate /force | Out-Null
    Write-LogMessage "Group policy refreshed" "Green"
} catch {
    Write-LogMessage "Failed to refresh group policy: $($_.Exception.Message)" "Red"
}

Write-LogMessage "=== REGISTRY REPAIR COMPLETED ===" "Cyan"
Write-LogMessage "Registry-level Microsoft Store restoration finished" "Green"
Write-LogMessage "Recommendation: Restart computer for registry changes to take effect" "Yellow"

Read-Host "Press Enter to continue"

# Fresh Microsoft Store Installation Script for Windows 10/11
# This script installs Microsoft Store from scratch
# MUST BE RUN AS ADMINISTRATOR

Write-Host "================================================================" -ForegroundColor Cyan
Write-Host "    FRESH MICROSOFT STORE INSTALLATION" -ForegroundColor Cyan
Write-Host "================================================================" -ForegroundColor Cyan

# Check administrator privileges
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "ERROR: This script must be run as Administrator!" -ForegroundColor Red
    Write-Host "Please right-click PowerShell and select 'Run as Administrator'" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "Administrator privileges confirmed." -ForegroundColor Green

function Write-LogMessage {
    param([string]$Message, [string]$Color = "White")
    $timestamp = Get-Date -Format "HH:mm:ss"
    Write-Host "[$timestamp] $Message" -ForegroundColor $Color
}

Write-LogMessage "Starting fresh Microsoft Store installation..." "Cyan"

# Step 1: Check current state
Write-LogMessage "Step 1: Checking current Microsoft Store state..." "Yellow"
$currentStore = Get-AppxPackage Microsoft.WindowsStore*
if ($currentStore) {
    Write-LogMessage "Microsoft Store already installed:" "Green"
    $currentStore | ForEach-Object {
        Write-LogMessage "  - $($_.Name) v$($_.Version)" "Cyan"
    }
    Write-LogMessage "Installation may not be necessary, but continuing with repair..." "Yellow"
} else {
    Write-LogMessage "Microsoft Store not found - proceeding with installation" "Yellow"
}

# Step 2: Install using PowerShell AppX method
Write-LogMessage "Step 2: Installing Microsoft Store using PowerShell..." "Yellow"
try {
    # Try to find and register existing Store packages
    $allUsersStore = Get-AppxPackage -AllUsers Microsoft.WindowsStore* -ErrorAction SilentlyContinue
    if ($allUsersStore) {
        Write-LogMessage "Found existing Store packages for all users" "Cyan"
        $allUsersStore | ForEach-Object {
            $manifestPath = "$($_.InstallLocation)\AppXManifest.xml"
            if (Test-Path $manifestPath) {
                Write-LogMessage "Registering: $($_.Name)" "Cyan"
                Add-AppxPackage -DisableDevelopmentMode -Register $manifestPath -ErrorAction SilentlyContinue
            }
        }
        Write-LogMessage "PowerShell registration completed" "Green"
    } else {
        Write-LogMessage "No existing Store packages found for registration" "Yellow"
    }
} catch {
    Write-LogMessage "PowerShell method failed: $($_.Exception.Message)" "Red"
}

# Step 3: Install using DISM
Write-LogMessage "Step 3: Installing Microsoft Store using DISM..." "Yellow"
try {
    $dismResult = dism /online /add-capability /capabilityname:Microsoft.Windows.Store~~~~0.0.1.0
    if ($LASTEXITCODE -eq 0) {
        Write-LogMessage "DISM installation completed successfully" "Green"
    } else {
        Write-LogMessage "DISM installation failed (Exit code: $LASTEXITCODE)" "Red"
    }
} catch {
    Write-LogMessage "DISM method failed: $($_.Exception.Message)" "Red"
}

# Step 4: Try Windows Package Manager (winget)
Write-LogMessage "Step 4: Attempting installation via Windows Package Manager..." "Yellow"
try {
    $wingetPath = Get-Command winget -ErrorAction SilentlyContinue
    if ($wingetPath) {
        Write-LogMessage "Winget found, attempting installation..." "Cyan"
        $wingetResult = winget install "Microsoft Store" --accept-package-agreements --accept-source-agreements
        if ($LASTEXITCODE -eq 0) {
            Write-LogMessage "Winget installation completed" "Green"
        } else {
            Write-LogMessage "Winget installation failed" "Yellow"
        }
    } else {
        Write-LogMessage "Winget not available on this system" "Yellow"
    }
} catch {
    Write-LogMessage "Winget method failed: $($_.Exception.Message)" "Red"
}

# Step 5: Reset Windows Store cache
Write-LogMessage "Step 5: Resetting Windows Store cache..." "Yellow"
try {
    Start-Process "wsreset.exe" -Wait -WindowStyle Hidden -ErrorAction SilentlyContinue
    Write-LogMessage "Windows Store cache reset completed" "Green"
} catch {
    Write-LogMessage "Cache reset failed: $($_.Exception.Message)" "Yellow"
}

# Step 6: Verification
Write-LogMessage "Step 6: Verifying installation..." "Yellow"
Start-Sleep -Seconds 3

$finalCheck = Get-AppxPackage Microsoft.WindowsStore*
if ($finalCheck) {
    Write-LogMessage "================================================================" "Green"
    Write-LogMessage "SUCCESS! MICROSOFT STORE HAS BEEN INSTALLED!" "Green"
    Write-LogMessage "================================================================" "Green"
    
    $finalCheck | ForEach-Object {
        Write-LogMessage "Installed: $($_.Name) v$($_.Version)" "Green"
    }
    
    # Test store launch
    Write-LogMessage "Testing Microsoft Store launch..." "Yellow"
    try {
        Start-Process "ms-windows-store:" -ErrorAction Stop
        Write-LogMessage "Microsoft Store launched successfully!" "Green"
    } catch {
        Write-LogMessage "Store installed but launch test failed: $($_.Exception.Message)" "Yellow"
    }
    
} else {
    Write-LogMessage "================================================================" "Red"
    Write-LogMessage "INSTALLATION INCOMPLETE" "Red"
    Write-LogMessage "================================================================" "Red"
    Write-LogMessage "Microsoft Store was not successfully installed" "Red"
    Write-LogMessage "Please try the manual methods or contact support" "Yellow"
}

Write-LogMessage "================================================================" "Cyan"
Write-LogMessage "INSTALLATION SCRIPT COMPLETED" "Cyan"
Write-LogMessage "================================================================" "Cyan"

Write-LogMessage "IMPORTANT: Please restart your computer for all changes to take effect" "Yellow"

$reboot = Read-Host "Would you like to restart now? (y/n)"
if ($reboot -eq "y" -or $reboot -eq "Y") {
    Write-LogMessage "Restarting computer..." "Green"
    Restart-Computer -Force
} else {
    Write-LogMessage "Please restart manually when convenient" "Yellow"
}

Write-LogMessage "Script completed." "Cyan"
Read-Host "Press Enter to exit"
